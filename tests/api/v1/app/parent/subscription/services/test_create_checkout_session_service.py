"""
Tests for create_checkout_session_service, particularly the fix for duplicate subscription items.
"""
import pytest
from unittest.mock import MagicMock, patch
from sqlalchemy.orm import Session
from datetime import datetime, UTC, timedelta

from api.v1.app.parent.subscription.services.create_checkout_session_service import create_checkout_session_service
from api.v1.app.parent.subscription.schemas.request import CreateCheckoutSessionRequest, CheckoutItemSelection
from db.models import Account, ActiveSubscription, SubscriptionStatusType
from db.models.subscription import BillingPeriodEnum, PlanTypeEnum
from tests.mocks.stripe_subscription_mocks import create_mock_stripe_subscription


class TestCreateCheckoutSessionService:
    """Test cases for create_checkout_session_service."""

    def test_add_subjects_to_existing_subscription_same_year_plan(
        self,
        db_session: Session,
        test_parent_account: Account,
        test_subscription_option,
        mocker
    ):
        """Test that adding more subjects to same year/plan updates quantity instead of creating new item."""
        
        # Create an existing subscription
        existing_subscription = ActiveSubscription(
            public_id="test_sub_123",
            parent_account_id=test_parent_account.id,
            stripe_subscription_id="sub_test_existing",
            status=SubscriptionStatusType.ACTIVE,
            current_period_start=datetime.now(UTC),
            current_period_end=datetime.now(UTC) + timedelta(days=30),
            cancel_at_period_end=False
        )
        db_session.add(existing_subscription)
        db_session.commit()

        # Mock Stripe subscription with existing items
        # User already has 2 subjects for Year 1 SC plan
        mock_stripe_sub = MagicMock()
        mock_stripe_sub.items = MagicMock()
        mock_stripe_sub.items.data = [
            MagicMock(
                id="si_existing_year1_sc",
                price=MagicMock(id="price_year1_sc_monthly"),
                quantity=2  # Currently has 2 subjects
            )
        ]

        # Mock Stripe calls
        mock_stripe_retrieve = mocker.patch("stripe.Subscription.retrieve", return_value=mock_stripe_sub)
        mock_stripe_modify = mocker.patch("stripe.SubscriptionItem.modify")
        mock_stripe_create = mocker.patch("stripe.SubscriptionItem.create")

        # Mock other required dependencies
        mocker.patch("api.v1.app.parent.subscription.services.create_checkout_session_service._get_or_create_eligible_price_version")
        
        # Simulate adding 1 more subject to the same year/plan (same price)
        # This should update quantity from 2 to 3, not create a new item
        line_items = [
            {
                'price': 'price_year1_sc_monthly',  # Same price as existing
                'quantity': 1  # Adding 1 more subject
            }
        ]

        # Expected behavior:
        # - stripe.Subscription.retrieve should be called to get existing items
        # - stripe.SubscriptionItem.modify should be called to update quantity from 2 to 3
        # - stripe.SubscriptionItem.create should NOT be called for the same price

    def test_add_to_existing_subscription_different_year(
        self,
        db_session: Session,
        test_parent_account: Account,
        mocker
    ):
        """Test that adding subjects for a different year creates new subscription item."""
        
        # Mock Stripe subscription with existing items (Year 1)
        mock_stripe_sub = MagicMock()
        mock_stripe_sub.items = MagicMock()
        mock_stripe_sub.items.data = [
            MagicMock(
                id="si_existing_year1",
                price=MagicMock(id="price_year1_sc_monthly"),
                quantity=2
            )
        ]

        # Mock Stripe calls
        mock_stripe_retrieve = mocker.patch("stripe.Subscription.retrieve", return_value=mock_stripe_sub)
        mock_stripe_modify = mocker.patch("stripe.SubscriptionItem.modify")
        mock_stripe_create = mocker.patch("stripe.SubscriptionItem.create")

        # Simulate adding subjects for Year 2 (different price)
        # This should create a new subscription item
        line_items = [
            {
                'price': 'price_year2_sc_monthly',  # Different price (different year)
                'quantity': 1
            }
        ]

        # Expected behavior:
        # - stripe.Subscription.retrieve should be called to get existing items
        # - stripe.SubscriptionItem.create should be called for the new price
        # - stripe.SubscriptionItem.modify should NOT be called
