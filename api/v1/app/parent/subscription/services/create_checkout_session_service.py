import stripe
from sqlalchemy.orm import Session as SyncSession
from core.config.settings import settings 
from sqlalchemy import select
from loguru import logger

from db.models import Account, Subject, StripeCheckoutSessionData, DiscountCode, Year
from db.models.subscription import BillingPeriodEnum, PlanTypeEnum, SubscriptionOption, ActiveSubscription, ActiveSubscriptionPlanLink, SubscriptionStatusType
from ..schemas.request import CreateCheckoutSessionRequest
from ..schemas.response import CreateCheckoutSessionResponse
from core.exception_handling.exceptions.custom_exceptions import (
    ValidationError, ServiceError, ExternalServiceError, AuthenticationError
)
from datetime import datetime, UTC
import time
from api.v1.common.schemas import AppErrorCode
from .utils import _get_or_create_eligible_price_version

stripe.api_key = settings.STRIPE_API_KEY 
DOMAIN_URL = settings.FRONTEND_URL 

LU_TAX_RATE = settings.LU_TAX_RATE  
DE_TAX_RATE = settings.DE_TAX_RATE 
FR_TAX_RATE = settings.FR_TAX_RATE 
BE_TAX_RATE = settings.BE_TAX_RATE 
DK_TAX_RATE = settings.DK_TAX_RATE 

# Create dynamic tax rates array (filtering out None values)
dynamic_tax_rates = [
    rate for rate in [
        LU_TAX_RATE, DE_TAX_RATE, FR_TAX_RATE, BE_TAX_RATE, DK_TAX_RATE
    ] if rate is not None
]

def create_checkout_session_service(
    db: SyncSession,
    parent_public_id: str,
    new_session_data: CreateCheckoutSessionRequest
) -> CreateCheckoutSessionResponse:
    logger.info(
        "Creating Stripe checkout session for parent: {} with {} selections.",
        parent_public_id, 
        len(new_session_data.selections)
    )

    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    relevant_account = db.execute(account_stmt).scalar_one_or_none()

    if not relevant_account:
        logger.warning("Parent account not found: {} for checkout session.", parent_public_id)
        raise AuthenticationError(
            message="Parent account not found.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )

    if not relevant_account.stripe_customer_id:
        logger.info(
            "Stripe customer ID missing for parent account: {}. Attempting to create one.",
            parent_public_id
        )
        try:
            customer = stripe.Customer.create(
                email=relevant_account.email,
                name=f"Parent Account {relevant_account.public_id}",
                metadata={"app_account_public_id": relevant_account.public_id}
            )
            relevant_account.stripe_customer_id = customer.id
            db.add(relevant_account)
            logger.info(
                "Successfully created Stripe customer ID {} for account {}",
                customer.id,
                relevant_account.public_id
            )
        except stripe.error.StripeError as e:
            logger.error(
                "Failed to create Stripe customer for {}: {}",
                relevant_account.public_id,
                str(e)
            )
            raise ExternalServiceError(
                message="Could not configure billing information. Please try again or contact support.",
                error_code=AppErrorCode.STRIPE_ERROR,
                original_exception=e
            )
        except Exception as e:
            logger.error(
                "Unexpected error creating Stripe customer or preparing DB for {}: {}",
                relevant_account.public_id,
                str(e)
            )
            raise ServiceError(
                message="An unexpected error occurred while setting up billing.",
                error_code=AppErrorCode.SERVICE_ERROR
            )

    price_version = _get_or_create_eligible_price_version(db, relevant_account)

    # Collect requested years and billing periods
    requested_year_ids = set()
    requested_billing_periods = set()
    
    # Track if discount was successfully applied
    checkout_discount_applied = False

    line_items = []

    line_item_details_for_db = []

    for selection in new_session_data.selections:
        year_stmt = select(Year).where(Year.public_id == selection.year_public_id)
        year_db = db.execute(year_stmt).scalar_one_or_none()
        if not year_db:
            raise ValidationError(
                "Year with public_id '{}' not found.".format(selection.year_public_id),
                error_code=AppErrorCode.INVALID_REQUEST
            )
        
        # Track requested years and billing periods
        requested_year_ids.add(year_db.id)
        requested_billing_periods.add(selection.chosen_billing_period)

        # Determine plan type and quantity based on the selection
        if selection.full_year_plan:
            plan_type_to_find = PlanTypeEnum.YF
            quantity_for_stripe = 1
        else:
            plan_type_to_find = PlanTypeEnum.SC
            quantity_for_stripe = len(selection.subject_public_ids)
            if quantity_for_stripe == 0:
                raise ValidationError(
                    "For SC plan selection, at least one subject must be provided.",
                    error_code=AppErrorCode.INVALID_REQUEST
                )

            # Validate subjects belong to the selected year and are active
            stmt_subjects = select(Subject).where(
                Subject.public_id.in_(selection.subject_public_ids),
                Subject.is_active
            )
            selected_subject_orms = db.execute(stmt_subjects).scalars().all()
            if len(selected_subject_orms) != quantity_for_stripe:
                raise ValidationError(
                    "One or more selected subjects are invalid, inactive, or not found.",
                    error_code=AppErrorCode.INVALID_REQUEST
                )
            for subj in selected_subject_orms:
                if subj.year_id != year_db.id:
                    raise ValidationError(
                        "Subject does not belong to selected year.",
                        error_code=AppErrorCode.INVALID_REQUEST
                    )

        relevant_subscription_option_stmt = select(SubscriptionOption).where(
            SubscriptionOption.year_id == year_db.id,
            SubscriptionOption.price_version_id == price_version.id,
            SubscriptionOption.is_active
        )

        relevant_subscription_option = db.execute(relevant_subscription_option_stmt).scalar_one_or_none()

        if not relevant_subscription_option:
            logger.warning(
                "Active SubscriptionOption not found for Year {} and Price Version {}.",
                year_db.name,
                price_version.name
            )
            raise ValidationError(
                "Plan for Year {} (Price Version: {}) is not available.".format(
                    year_db.name, price_version.name
                ),
                error_code=AppErrorCode.INVALID_REQUEST
            )

        relevant_stripe_price_id = None
        if selection.full_year_plan:
            if selection.chosen_billing_period == BillingPeriodEnum.MONTHLY:
                relevant_stripe_price_id = relevant_subscription_option.yf_monthly_stripe_price_id
            else:
                relevant_stripe_price_id = relevant_subscription_option.yf_yearly_stripe_price_id
        else:
            if selection.chosen_billing_period == BillingPeriodEnum.MONTHLY:
                relevant_stripe_price_id = relevant_subscription_option.sc_monthly_stripe_price_id
            else:
                relevant_stripe_price_id = relevant_subscription_option.sc_yearly_stripe_price_id

        if not relevant_stripe_price_id:
            logger.warning(
                "Stripe price ID missing for Year {} and Type {}.",
                year_db.name,
                selection.chosen_billing_period.value
            )
            raise ValidationError(
                "Plan for Year {} (Type: {}) is not available.".format(
                    year_db.name, selection.chosen_billing_period.value
                ),
                error_code=AppErrorCode.INVALID_REQUEST
            )

        line_item_stripe_data = {
            'price': relevant_stripe_price_id,
            'quantity': quantity_for_stripe,
        }
        # Add dynamic tax rates to let customer select appropriate rate
        if dynamic_tax_rates:
            line_item_stripe_data['dynamic_tax_rates'] = dynamic_tax_rates
        # Note: Stripe doesn't allow metadata on line items in checkout sessions
        # All metadata is stored in session-level metadata and line_item_details_json

        line_items.append(line_item_stripe_data)

        # If we have the full year case, we make sure that we get all the subjects for the year.
        if selection.full_year_plan:
            stmt_year_subjects = select(Subject).where(Subject.year_id == year_db.id)
            year_subject_orms = db.execute(stmt_year_subjects).scalars().all()
            selection.subject_public_ids = [subj.public_id for subj in year_subject_orms]

        # Store details for StripeCheckoutSessionData
        item_detail_for_db = {
            "subscription_option_public_id": relevant_subscription_option.public_id,
            "chosen_stripe_price_id": relevant_stripe_price_id,
            "chosen_billing_period": selection.chosen_billing_period.value,
            "quantity": quantity_for_stripe,
            "selected_subject_public_ids": selection.subject_public_ids or [],
            "year_public_id": year_db.public_id,
            "derived_plan_type": plan_type_to_find.value
        }
        line_item_details_for_db.append(item_detail_for_db)

    # Check for existing subscription for the requested years
    existing_subscription_for_years = None
    if requested_year_ids:
        existing_subscriptions = db.execute(
            select(ActiveSubscription).where(
                ActiveSubscription.parent_account_id == relevant_account.id,
                ActiveSubscription.status.in_([
                    SubscriptionStatusType.ACTIVE,
                    SubscriptionStatusType.TRIALING
                ])
            )
        ).scalars().all()
        
        # Check if any existing subscription covers the requested years
        for subscription in existing_subscriptions:
            subscription_plan_links = db.execute(
                select(ActiveSubscriptionPlanLink).where(
                    ActiveSubscriptionPlanLink.active_subscription_id == subscription.id
                )
            ).scalars().all()
            
            # Get years covered by this subscription
            subscription_year_ids = set()
            subscription_billing_periods = set()
            
            for link in subscription_plan_links:
                link_option = db.execute(
                    select(SubscriptionOption).where(SubscriptionOption.id == link.subscription_option_id)
                ).scalar_one()
                subscription_year_ids.add(link_option.year_id)
                subscription_billing_periods.add(link.chosen_billing_period)
            
            # Check if this subscription covers any of the requested years
            if subscription_year_ids.intersection(requested_year_ids):
                # Validate billing periods match
                if subscription_billing_periods != requested_billing_periods:
                    raise ValidationError(
                        f"Cannot add subjects with different billing periods to existing subscription. "
                        f"Existing subscription has {[p.value for p in subscription_billing_periods]} "
                        f"but you're trying to add {[p.value for p in requested_billing_periods]}.",
                        error_code=AppErrorCode.BILLING_PERIOD_MISMATCH
                    )
                
                existing_subscription_for_years = subscription
                logger.info(
                    f"Found existing subscription {subscription.public_id} for requested years. "
                    f"Will add items directly to existing subscription."
                )
                break

    success_url = new_session_data.success_url_override \
        or f"{DOMAIN_URL}/{new_session_data.language}/parent/subscriptions?success=true&session_id={{CHECKOUT_SESSION_ID}}"
    cancel_url = new_session_data.cancel_url_override \
        or f"{DOMAIN_URL}/{new_session_data.language}/parent/subscriptions?canceled=true"

    # Metadata for the Checkout Session itself (limited size)
    checkout_session_stripe_metadata = {
        "parent_account_public_id": parent_public_id,
        "price_version_public_id": price_version.public_id
    }

    # If we found an existing subscription for these years, add items directly
    if existing_subscription_for_years:
        logger.info(
            f"Adding {len(line_items)} items directly to existing subscription "
            f"{existing_subscription_for_years.stripe_subscription_id}"
        )
        
        # Handle discount code for existing subscription
        stripe_discount_id = None
        if new_session_data.discount_code:
            db_discount_code = db.execute(
                select(DiscountCode).where(
                    DiscountCode.public_code == new_session_data.discount_code.strip().upper(),
                    DiscountCode.is_active
                )
            ).scalar_one_or_none()
            
            if db_discount_code:
                # Get billing period (we know they're all the same from validation above)
                billing_period = next(iter(requested_billing_periods))
                
                if billing_period == BillingPeriodEnum.MONTHLY:
                    stripe_discount_id = db_discount_code.stripe_monthly_id
                else:  # YEARLY
                    stripe_discount_id = db_discount_code.stripe_yearly_id
                
                if stripe_discount_id:
                    logger.info(f"Applying discount {new_session_data.discount_code} to existing subscription")
                else:
                    logger.warning(f"Discount code valid but no Stripe ID for {billing_period.value} billing period")
            else:
                logger.warning(f"Discount code {new_session_data.discount_code} not found or inactive")
        
        # Add items to existing subscription
        try:
            # Apply discount if available
            if stripe_discount_id:
                try:
                    stripe.Subscription.modify(
                        existing_subscription_for_years.stripe_subscription_id,
                        coupon=stripe_discount_id
                    )
                    logger.info(f"Applied discount to subscription {existing_subscription_for_years.stripe_subscription_id}")
                except stripe.error.StripeError as discount_error:
                    logger.warning(f"Failed to apply discount: {discount_error}")
            
            # Retrieve existing subscription with items to check for duplicates
            stripe_subscription = stripe.Subscription.retrieve(
                existing_subscription_for_years.stripe_subscription_id,
                expand=["items.data"]
            )

            # Create a map of existing price IDs to their subscription item IDs and quantities
            existing_items_map = {}
            for item in stripe_subscription.items.data:
                existing_items_map[item.price.id] = {
                    'subscription_item_id': item.id,
                    'current_quantity': item.quantity
                }

            # Process each line item - either update existing or create new
            for line_item in line_items:
                stripe_price_id = line_item['price']
                desired_total_quantity = line_item['quantity']  # This is the total desired quantity, not additional

                if stripe_price_id in existing_items_map:
                    # Price already exists - update quantity to match user's total selection
                    # This handles the case where user is changing subjects for the same year/plan
                    existing_item = existing_items_map[stripe_price_id]
                    current_quantity = existing_item['current_quantity']

                    # Only update if the desired quantity is different from current
                    if desired_total_quantity != current_quantity:
                        updated_item = stripe.SubscriptionItem.modify(
                            existing_item['subscription_item_id'],
                            quantity=desired_total_quantity,
                            proration_behavior='always_invoice'  # Immediate billing for upgrades/downgrades
                        )

                        if desired_total_quantity > current_quantity:
                            action_description = f"upgraded from {current_quantity} to {desired_total_quantity} subjects"
                        else:
                            action_description = f"downgraded from {current_quantity} to {desired_total_quantity} subjects"

                        logger.info(
                            f"Updated existing subscription item {existing_item['subscription_item_id']} "
                            f"quantity to {desired_total_quantity} for price {stripe_price_id} "
                            f"({action_description})"
                        )
                    else:
                        logger.info(
                            f"Subscription item {existing_item['subscription_item_id']} already has "
                            f"correct quantity {desired_total_quantity} for price {stripe_price_id} - no update needed"
                        )
                else:
                    # Price doesn't exist - create new subscription item
                    # This handles different year/plan/billing period combinations
                    subscription_item = stripe.SubscriptionItem.create(
                        subscription=existing_subscription_for_years.stripe_subscription_id,
                        price=stripe_price_id,
                        quantity=desired_total_quantity,
                        proration_behavior='always_invoice'  # Immediate billing for new items
                    )

                    logger.info(
                        f"Added new subscription item {subscription_item.id} with quantity {desired_total_quantity} "
                        f"for price {stripe_price_id} to subscription {existing_subscription_for_years.stripe_subscription_id}"
                    )
            
            # Save session data for tracking
            checkout_session_db_data = StripeCheckoutSessionData(
                stripe_checkout_session_id=f"direct_add_{existing_subscription_for_years.stripe_subscription_id}_{int(time.time())}",
                line_item_details_json=line_item_details_for_db,
                parent_account_public_id=parent_public_id,
                processed_at=datetime.now(UTC)  # Mark as processed
            )
            db.add(checkout_session_db_data)
            db.commit()
            
            discount_message = f" with discount '{new_session_data.discount_code}'" if stripe_discount_id else ""
            
            return CreateCheckoutSessionResponse(
                checkout_session_id="direct_subscription_update",
                checkout_session_url=f"{DOMAIN_URL}/{new_session_data.language}/parent/subscriptions?success=true&direct_update=true",
                flow_type="direct_update",
                message=f"Successfully added {len(line_items)} item(s) to your existing subscription{discount_message}!",
                items_added=len(line_items),
                discount_applied=stripe_discount_id is not None
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to add items to existing subscription: {e}")
            raise ExternalServiceError(
                message=f"Failed to add items to existing subscription: {e.user_message if hasattr(e, 'user_message') else str(e)}",
                error_code=AppErrorCode.STRIPE_ERROR,
                original_exception=e
            )

    # Create new checkout session for new subscription
    checkout_session_params = {
        'customer': relevant_account.stripe_customer_id,
        'line_items': line_items,
        'mode': 'subscription',
        'success_url': success_url,
        'cancel_url': cancel_url,
        'metadata': checkout_session_stripe_metadata,
    }

    if new_session_data.discount_code:
        db_discount_code = db.execute(
            select(DiscountCode).where(
                DiscountCode.public_code == new_session_data.discount_code.strip().upper(),
                DiscountCode.is_active
            )
        ).scalar_one_or_none()
        
        if db_discount_code:
            # Determine the billing period from selections
            # All selections should have the same billing period for a single checkout
            billing_periods = {selection.chosen_billing_period for selection in new_session_data.selections}
            if len(billing_periods) > 1:
                logger.warning(
                    "Mixed billing periods in checkout: {}. Cannot apply discount.",
                    billing_periods
                )
                raise ValidationError(
                    "Cannot apply discount to mixed billing periods in a single checkout.",
                    error_code=AppErrorCode.INVALID_REQUEST
                )
            
            billing_period = billing_periods.pop()
            
            # Select appropriate Stripe ID based on billing period
            stripe_discount_id = None
            if billing_period == BillingPeriodEnum.MONTHLY:
                stripe_discount_id = db_discount_code.stripe_monthly_id
                if not stripe_discount_id:
                    logger.warning(
                        "Discount code {} has no monthly Stripe ID but monthly plan selected.",
                        new_session_data.discount_code
                    )
            else:  # YEARLY
                stripe_discount_id = db_discount_code.stripe_yearly_id
                if not stripe_discount_id:
                    logger.warning(
                        "Discount code {} has no yearly Stripe ID but yearly plan selected.",
                        new_session_data.discount_code
                    )
            
            if stripe_discount_id:
                checkout_session_params['discounts'] = [{'coupon': stripe_discount_id}]
                checkout_discount_applied = True
                logger.info(
                    "Applying {} discount (Stripe ID: {}) to checkout.",
                    billing_period.value,
                    stripe_discount_id
                )
            else:
                logger.warning(
                    "Discount code {} valid but no Stripe ID for {} billing period.",
                    new_session_data.discount_code,
                    billing_period.value
                )
        else:
            logger.warning("Discount code {} not found or inactive.", new_session_data.discount_code)

    try:
        checkout_session = stripe.checkout.Session.create(**checkout_session_params)
        logger.info(
            "Stripe checkout session {} created successfully for parent {}",
            checkout_session.id,
            parent_public_id
        )

        checkout_session_db_data = StripeCheckoutSessionData(
            stripe_checkout_session_id=checkout_session.id,
            line_item_details_json=line_item_details_for_db,
            parent_account_public_id=parent_public_id
        )
        db.add(checkout_session_db_data)
        db.commit()  # Explicitly commit to prevent race condition with webhook

        return CreateCheckoutSessionResponse(
            checkout_session_id=checkout_session.id,
            checkout_session_url=checkout_session.url,
            flow_type="checkout",
            message="Redirecting to Stripe checkout...",
            items_added=len(line_items),
            discount_applied=checkout_discount_applied
        )
    except stripe.error.StripeError as e:
        logger.error(
            "Stripe API error creating checkout session for parent {}: {}",
            parent_public_id,
            str(e),
            exc_info=True
        )
        user_message = (
            f"Payment gateway error: {e.user_message}" 
            if hasattr(e, 'user_message') and e.user_message 
            else "Could not initiate payment. Please try again."
        )
        raise ExternalServiceError(
            message=user_message,
            error_code=AppErrorCode.STRIPE_ERROR,
            original_exception=e
        )
    except Exception as e:
        logger.error(
            "Unexpected error creating checkout session for parent {}: {}",
            parent_public_id,
            str(e),
            exc_info=True
        )
        raise ServiceError(
            message="An unexpected error occurred while creating the checkout session.",
            error_code=AppErrorCode.SERVICE_ERROR
        )